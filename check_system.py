#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
فحص شامل للنظام والأدوات
System and Tools Comprehensive Check
"""

import sys
import os
import subprocess
from pathlib import Path

def check_python():
    """فحص إصدار Python"""
    print("🐍 فحص Python:")
    print(f"   الإصدار: {sys.version}")
    print(f"   المسار: {sys.executable}")
    
    if sys.version_info >= (3, 8):
        print("   ✅ إصدار Python مناسب")
    else:
        print("   ❌ يتطلب Python 3.8 أو أحدث")
    print()

def check_whisper():
    """فحص تثبيت Whisper والنموذج المطلوب"""
    print("🎤 فحص Whisper:")
    try:
        import whisper
        print(f"   ✅ Whisper مثبت")
        print(f"   الإصدار: {whisper.__version__ if hasattr(whisper, '__version__') else 'غير محدد'}")
        
        # فحص نموذج 'small' فقط
        model_name = "small"
        print(f"   🔍 فحص نموذج '{model_name}':")
        
        # تحديد مسار مخصص لتحميل النماذج داخل مجلد المشروع
        download_dir = os.path.join(os.getcwd(), "models")
        os.makedirs(download_dir, exist_ok=True)
        
        model_path = os.path.join(download_dir, f"{model_name}.pt")

        if os.path.exists(model_path):
            print(f"     ✅ نموذج '{model_name}' موجود بالفعل.")
            try:
                whisper.load_model(model_name, download_root=download_dir)
                print(f"     ✅ تم التحقق من النموذج بنجاح.")
            except Exception as e:
                print(f"     ❌ خطأ عند تحميل النموذج الموجود: {e}")
        else:
            print(f"     ⌛ نموذج '{model_name}' غير موجود، سيتم محاولة تحميله الآن...")
            try:
                whisper.load_model(model_name, download_root=download_dir)
                print(f"     ✅ تم تحميل نموذج '{model_name}' بنجاح!")
            except Exception as e:
                print(f"     ❌ فشل تحميل نموذج '{model_name}': {e}")
                
    except ImportError:
        print("   ❌ Whisper غير مثبت. يرجى تشغيل: pip install -r requirements.txt")
    print()

def check_ffmpeg():
    """فحص تثبيت ffmpeg"""
    print("🎬 فحص ffmpeg:")
    try:
        result = subprocess.run(['ffmpeg', '-version'], 
                              capture_output=True, text=True, timeout=10)
        if result.returncode == 0:
            version_line = result.stdout.split('\n')[0]
            print(f"   ✅ ffmpeg مثبت: {version_line}")
        else:
            print("   ❌ ffmpeg غير مثبت أو لا يعمل")
    except (subprocess.TimeoutExpired, FileNotFoundError):
        print("   ❌ ffmpeg غير مثبت")
        print("   تثبيت ffmpeg:")
        print("   Windows: choco install ffmpeg أو scoop install ffmpeg")
        print("   Linux: sudo apt install ffmpeg")
        print("   macOS: brew install ffmpeg")
    print()

def check_files():
    """فحص ملفات الأداة"""
    print("📁 فحص ملفات الأداة:")
    required_files = [
        "whisper_test.py",
        "advanced_converter.py", 
        "test_whisper.py",
        "requirements.txt",
        "README.md",
        "convert_to_text.bat"
    ]
    
    for file in required_files:
        if os.path.exists(file):
            size = os.path.getsize(file)
            print(f"   ✅ {file} ({size} bytes)")
        else:
            print(f"   ❌ {file} مفقود")
    print()

def check_dependencies():
    """فحص المكتبات المطلوبة"""
    print("📦 فحص المكتبات:")
    dependencies = [
        "torch",
        "torchaudio", 
        "numpy",
        "tiktoken"
    ]
    
    for dep in dependencies:
        try:
            __import__(dep)
            print(f"   ✅ {dep}")
        except ImportError:
            print(f"   ❌ {dep} غير مثبت")
    print()

def check_disk_space():
    """فحص مساحة القرص"""
    print("💾 فحص مساحة القرص:")
    try:
        import shutil
        total, used, free = shutil.disk_usage(".")
        total_gb = total // (1024**3)
        free_gb = free // (1024**3)
        print(f"   المساحة الإجمالية: {total_gb} GB")
        print(f"   المساحة المتاحة: {free_gb} GB")
        
        if free_gb > 5:
            print("   ✅ مساحة كافية")
        else:
            print("   ⚠️ مساحة قليلة (يُنصح بـ 5GB على الأقل)")
    except:
        print("   ❌ لا يمكن فحص المساحة")
    print()

def main():
    """الدالة الرئيسية"""
    print("=" * 50)
    print("    فحص شامل للنظام والأدوات")
    print("    System and Tools Check")
    print("=" * 50)
    print()
    
    check_python()
    check_whisper()
    check_ffmpeg()
    check_files()
    check_dependencies()
    check_disk_space()
    
    print("=" * 50)
    print("انتهى الفحص!")
    print("Check completed!")
    print("=" * 50)

if __name__ == "__main__":
    main()
