#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ملف تشغيل محسن للتطبيق مع معالجة الأخطاء
Enhanced app runner with error handling
"""

import os
import sys
import subprocess
import time

def check_requirements():
    """فحص المتطلبات"""
    print("🔍 فحص المتطلبات...")
    
    try:
        import gradio
        print(f"✅ Gradio: {gradio.__version__}")
    except ImportError:
        print("❌ Gradio غير مثبت")
        return False
    
    try:
        import whisper
        print("✅ Whisper مثبت")
    except ImportError:
        print("❌ Whisper غير مثبت")
        return False
    
    return True

def check_model():
    """فحص النموذج"""
    print("🔍 فحص النموذج...")
    
    model_path = "./models/small.pt"
    if os.path.exists(model_path):
        size = os.path.getsize(model_path) / (1024 * 1024)
        print(f"✅ النموذج موجود: {size:.1f}MB")
        return True
    else:
        print("❌ النموذج غير موجود")
        return False

def run_app(app_file="app_simple.py"):
    """تشغيل التطبيق"""
    print(f"🚀 تشغيل {app_file}...")
    
    try:
        # تشغيل التطبيق
        result = subprocess.run([
            sys.executable, app_file
        ], capture_output=False, text=True)
        
        return result.returncode == 0
        
    except KeyboardInterrupt:
        print("\n⏹️ تم إيقاف التطبيق بواسطة المستخدم")
        return True
    except Exception as e:
        print(f"❌ خطأ في التشغيل: {e}")
        return False

def main():
    """الدالة الرئيسية"""
    print("=" * 50)
    print("🎤 محول الصوت إلى نص - تشغيل محسن")
    print("=" * 50)
    
    # فحص المتطلبات
    if not check_requirements():
        print("\n❌ يرجى تثبيت المتطلبات أولاً:")
        print("pip install -r requirements.txt")
        return
    
    # فحص النموذج
    model_exists = check_model()
    
    # اختيار ملف التطبيق
    if model_exists:
        app_file = "app.py"
        print("✅ سيتم استخدام النموذج المحلي")
    else:
        app_file = "app_simple.py"
        print("⚠️ سيتم تحميل نموذج صغير من الإنترنت")
    
    print(f"\n🚀 بدء تشغيل {app_file}...")
    print("💡 للإيقاف اضغط Ctrl+C")
    print("-" * 30)
    
    # تشغيل التطبيق
    success = run_app(app_file)
    
    if success:
        print("\n✅ تم إنهاء التطبيق بنجاح")
    else:
        print("\n❌ حدث خطأ في التطبيق")
        print("💡 جرب:")
        print("  - python app_simple.py")
        print("  - python app.py")

if __name__ == "__main__":
    main()
