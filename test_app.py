#!/usr/bin/env python3
import gradio as gr

def hello(name):
    return f"مرحبا {name}!"

with gr.Blocks() as app:
    gr.HTML("<h1>اختبار Gradio</h1>")
    
    with gr.Row():
        name_input = gr.Textbox(label="الاسم")
        output = gr.Textbox(label="الرد")
    
    btn = gr.<PERSON><PERSON>("اختبار")
    btn.click(hello, inputs=[name_input], outputs=[output])

if __name__ == "__main__":
    try:
        app.launch(
            server_name="127.0.0.1",
            server_port=7860,
            share=False,
            debug=False
        )
    except Exception as e:
        print(f"خطأ: {e}")
