---
title: Audio to Text Converter - Whisper Small
emoji: 🎤
colorFrom: blue
colorTo: purple
sdk: gradio
sdk_version: 4.44.1
app_file: app_hf.py
pinned: false
license: mit
---

# 🎤 محول الصوت إلى نص - Audio to Text Converter

تطبيق قوي لتحويل الملفات الصوتية والمرئية إلى نص باستخدام نموذج OpenAI Whisper Small.

A powerful application for converting audio and video files to text using OpenAI Whisper Small model.

## ✨ الميزات - Features

### 🎯 الميزات الرئيسية:
- **دقة عالية**: نموذج Whisper Small للحصول على أفضل دقة
- **متعدد اللغات**: دعم 99+ لغة بما في ذلك العربية والإنجليزية
- **سهل الاستخدام**: واجهة بسيطة وسريعة
- **API مدمج**: إمكانية الاستخدام البرمجي
- **مجاني**: استخدام مجاني بالكامل

### 📁 الملفات المدعومة:
- **الصوت**: MP3, WAV, M4A, FLAC, OGG
- **الفيديو**: MP4, AVI, MOV, MKV
- **الحد الأقصى**: 25 MB لكل ملف

## 🚀 كيفية الاستخدام - How to Use

### 1. رفع الملف:
- اضغط على "ارفع ملف صوتي أو فيديو"
- اختر الملف من جهازك أو سجل صوت مباشر

### 2. اختيار اللغة:
- اختر اللغة من القائمة المنسدلة
- أو اتركها على "تلقائي" للكشف التلقائي

### 3. التحويل:
- اضغط "تحويل إلى نص"
- انتظر حتى اكتمال المعالجة

### 4. النتائج:
- احصل على النص المستخرج
- راجع معلومات التحويل
- انسخ API Key للاستخدام البرمجي

## 🔑 API Usage

### Python Example:
```python
import requests
import json

# استبدل بالرابط الحقيقي لـ Space الخاص بك
API_URL = "https://YOUR_USERNAME-YOUR_SPACE_NAME.hf.space/api/predict"

# رفع ملف وتحويله
with open("audio.mp3", "rb") as f:
    files = {"data": f}
    data = {"data": [None, "Arabic"]}
    
response = requests.post(API_URL, files=files, data=json.dumps(data))
result = response.json()

print("النص:", result["data"][0])
print("المعلومات:", result["data"][1])
```

### cURL Example:
```bash
curl -X POST \
  -F "data=@audio.mp3" \
  -F "data=Arabic" \
  https://YOUR_USERNAME-YOUR_SPACE_NAME.hf.space/api/predict
```

## 🤖 معلومات النموذج - Model Information

- **النموذج**: OpenAI Whisper Small
- **الحجم**: ~244 MB
- **اللغات**: 99+ لغة مدعومة
- **الدقة**: عالية جداً للغة العربية والإنجليزية
- **السرعة**: متوسطة إلى سريعة

## 🛠️ التقنيات المستخدمة - Technologies

- **Python**: لغة البرمجة الأساسية
- **Gradio**: لبناء واجهة المستخدم
- **OpenAI Whisper**: نموذج تحويل الصوت إلى نص
- **PyTorch**: مكتبة التعلم العميق
- **Hugging Face Spaces**: منصة الاستضافة

## 📋 المتطلبات - Requirements

```
openai-whisper
gradio==4.44.1
torch
torchaudio
numpy
```

## 🔒 الأمان والخصوصية - Security & Privacy

- **API Key فريد**: يتم إنشاء مفتاح فريد لكل جلسة
- **عدم حفظ الملفات**: لا يتم حفظ الملفات المرفوعة
- **معالجة محلية**: تتم المعالجة على الخادم بدون إرسال لطرف ثالث
- **مفتوح المصدر**: الكود متاح للمراجعة

## 🎯 حالات الاستخدام - Use Cases

### للأفراد:
- تحويل المحاضرات والدروس
- تفريغ المقابلات والاجتماعات
- تحويل الملاحظات الصوتية
- ترجمة المحتوى الصوتي

### للمطورين:
- دمج في التطبيقات
- معالجة الملفات الصوتية بكميات كبيرة
- بناء حلول مخصصة
- التكامل مع أنظمة أخرى

## 🚀 التطوير والمساهمة - Development

### تشغيل محلي:
```bash
git clone [repository-url]
cd audio-to-text-converter
pip install -r requirements.txt
python app_hf.py
```

### المساهمة:
- Fork المشروع
- أنشئ branch جديد
- اعمل التحسينات
- أرسل Pull Request

## 📞 الدعم والمساعدة - Support

إذا واجهت أي مشاكل:
1. تأكد من أن الملف أقل من 25 MB
2. تأكد من أن تنسيق الملف مدعوم
3. جرب لغة مختلفة إذا لم تعمل اللغة التلقائية
4. أعد تحميل الصفحة إذا توقف التطبيق

## 📄 الترخيص - License

MIT License - استخدام مجاني للجميع

## 🙏 شكر وتقدير - Acknowledgments

- **OpenAI**: لنموذج Whisper الرائع
- **Hugging Face**: لمنصة Spaces المجانية
- **Gradio**: لمكتبة واجهة المستخدم السهلة

---

**تم التطوير بـ ❤️ للمجتمع العربي والعالمي**

**Developed with ❤️ for the Arabic and global community**
