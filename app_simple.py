#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
تطبيق Hugging Face Spaces مبسط
Simple Hugging Face Spaces App
"""

import gradio as gr
import whisper
import os
import secrets
from datetime import datetime

# إنشاء API Key بسيط
API_KEY = f"whisper-{secrets.token_hex(8)}"

# متغير للنموذج
model = None

def load_whisper_model():
    """تحميل نموذج Whisper"""
    global model
    if model is None:
        try:
            # محاولة تحميل النماذج المحلية أولاً
            if os.path.exists("./models/tiny.pt"):
                print("تحميل نموذج tiny محلي...")
                model = whisper.load_model("./models/tiny.pt")
            elif os.path.exists("./models/base.pt"):
                print("تحميل نموذج base محلي...")
                model = whisper.load_model("./models/base.pt")
            elif os.path.exists("./models/small.pt"):
                print("تحميل نموذج small محلي...")
                model = whisper.load_model("./models/small.pt")
            else:
                print("تحميل نموذج tiny من الإنترنت...")
                model = whisper.load_model("tiny")
            print("✅ تم تحميل النموذج بنجاح!")
        except Exception as e:
            print(f"❌ خطأ في تحميل النموذج: {e}")
            model = whisper.load_model("tiny")
    return model

def transcribe_file(audio_file, language="Arabic"):
    """تحويل الملف الصوتي إلى نص"""
    if audio_file is None:
        return "❌ يرجى رفع ملف صوتي أو فيديو", ""
    
    try:
        # تحميل النموذج
        whisper_model = load_whisper_model()
        
        # تحويل الملف
        if language == "تلقائي":
            result = whisper_model.transcribe(audio_file)
        else:
            result = whisper_model.transcribe(audio_file, language=language)
        
        # استخراج النتائج
        text = result["text"].strip()
        detected_language = result.get("language", "غير محدد")
        
        # معلومات التحويل
        info = f"""📊 معلومات التحويل:
🌍 اللغة المكتشفة: {detected_language}
📝 عدد الكلمات: {len(text.split())}
🕒 الوقت: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
🔑 API Key: {API_KEY}"""
        
        return text, info
        
    except Exception as e:
        return f"❌ خطأ في التحويل: {str(e)}", f"تفاصيل الخطأ: {str(e)}"

# تحميل النموذج عند بدء التطبيق
print("🚀 بدء تشغيل التطبيق...")
print(f"🔑 API Key: {API_KEY}")
load_whisper_model()

# واجهة Gradio
with gr.Blocks(title="محول الفيديو/الصوت إلى نص", theme=gr.themes.Soft()) as app:
    
    gr.HTML("""
    <div style="text-align: center;">
        <h1>🎤 محول الفيديو/الصوت إلى نص</h1>
        <h2>Video/Audio to Text Converter</h2>
        <p>مدعوم بـ OpenAI Whisper | Powered by OpenAI Whisper</p>
    </div>
    """)
    
    with gr.Tab("🎤 تحويل | Convert"):
        with gr.Row():
            with gr.Column():
                audio_input = gr.Audio(
                    label="رفع ملف صوتي أو فيديو | Upload Audio/Video File",
                    type="filepath"
                )
                
                language_choice = gr.Dropdown(
                    choices=["تلقائي", "Arabic", "English", "French", "Spanish", "German"],
                    value="Arabic",
                    label="اللغة | Language"
                )
                
                convert_btn = gr.Button("🚀 تحويل | Convert", variant="primary")
            
            with gr.Column():
                output_text = gr.Textbox(
                    label="النص المستخرج | Extracted Text",
                    lines=15,
                    max_lines=20
                )
                
                info_text = gr.Textbox(
                    label="معلومات التحويل | Conversion Info",
                    lines=8
                )
    
    with gr.Tab("🔑 API Key"):
        gr.HTML("<h3>🔐 معلومات API الخاصة بك</h3>")
        
        gr.Markdown(f"""
### 🔑 API Key الخاص بك:
```
{API_KEY}
```

### 🌐 استخدام API:
```python
import requests
import json

# استبدل بالرابط الحقيقي لـ Space الخاص بك
API_URL = "https://YOUR_USERNAME-YOUR_SPACE_NAME.hf.space/api/predict"

# رفع ملف وتحويله
with open("audio.mp3", "rb") as f:
    files = {{"data": f}}
    data = {{"data": [None, "Arabic"]}}
    
response = requests.post(API_URL, files=files, data=json.dumps(data))
result = response.json()
print(result["data"][0])  # النص المستخرج
```

### ⚠️ تنبيه أمني:
احتفظ بـ API Key في مكان آمن ولا تشاركه مع أحد
        """)
    
    # ربط الأحداث
    convert_btn.click(
        fn=transcribe_file,
        inputs=[audio_input, language_choice],
        outputs=[output_text, info_text]
    )

# تشغيل التطبيق
if __name__ == "__main__":
    print(f"\n🎉 التطبيق جاهز!")
    print(f"🔑 API Key: {API_KEY}")
    app.launch()
