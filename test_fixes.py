#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار الإصلاحات الجديدة
Test new fixes
"""

import sys
import os
import time

def test_model_loading():
    """اختبار تحميل النموذج مع الإصلاحات الجديدة"""
    print("🧪 اختبار تحميل النموذج...")
    
    try:
        sys.path.insert(0, './huggingface_upload')
        import app
        
        print("✅ تم استيراد التطبيق بنجاح")
        
        # اختبار تحميل النموذج
        print("🔄 اختبار تحميل النموذج...")
        start_time = time.time()
        
        model = app.load_whisper_model()
        
        load_time = time.time() - start_time
        print(f"✅ تم تحميل النموذج في {load_time:.2f} ثانية")
        
        # اختبار تحميل مرة أخرى (يجب أن يكون فورياً)
        print("🔄 اختبار إعادة التحميل...")
        start_time = time.time()
        
        model2 = app.load_whisper_model()
        
        reload_time = time.time() - start_time
        print(f"✅ إعادة التحميل في {reload_time:.2f} ثانية")
        
        if reload_time < 1.0:
            print("✅ التحقق من النموذج المحمل يعمل بشكل صحيح")
        else:
            print("⚠️ إعادة التحميل أبطأ من المتوقع")
        
        return model is not None
        
    except Exception as e:
        print(f"❌ خطأ في اختبار تحميل النموذج: {e}")
        return False

def test_app_import():
    """اختبار استيراد التطبيق بدون أخطاء"""
    print("\n🧪 اختبار استيراد التطبيق...")
    
    try:
        sys.path.insert(0, './huggingface_upload')
        import app
        
        print("✅ تم استيراد التطبيق بنجاح")
        
        # فحص المتغيرات المهمة
        if hasattr(app, 'API_KEY'):
            print(f"✅ API Key: {app.API_KEY}")
        
        if hasattr(app, 'app'):
            print("✅ تطبيق Gradio موجود")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في استيراد التطبيق: {e}")
        return False

def test_transcription():
    """اختبار دالة التحويل"""
    print("\n🎤 اختبار دالة التحويل...")
    
    try:
        sys.path.insert(0, './huggingface_upload')
        import app
        
        # اختبار مع ملف فارغ
        result = app.transcribe_audio(None, "Arabic")
        
        if isinstance(result, tuple) and len(result) == 3:
            text, info, api_info = result
            if "يرجى رفع ملف صوتي" in text:
                print("✅ دالة التحويل تعمل بشكل صحيح")
                return True
        
        print("❌ دالة التحويل لا تعمل كما متوقع")
        return False
        
    except Exception as e:
        print(f"❌ خطأ في اختبار دالة التحويل: {e}")
        return False

def check_files():
    """فحص الملفات"""
    print("\n📁 فحص الملفات...")
    
    required_files = [
        'huggingface_upload/app.py',
        'huggingface_upload/requirements.txt',
        'huggingface_upload/README.md',
        'huggingface_upload/models/small.pt'
    ]
    
    all_exist = True
    for file_path in required_files:
        if os.path.exists(file_path):
            if file_path.endswith('.pt'):
                file_size = os.path.getsize(file_path)
                size_mb = file_size / (1024 * 1024)
                print(f"✅ {file_path} ({size_mb:.1f}MB)")
            else:
                print(f"✅ {file_path}")
        else:
            print(f"❌ {file_path} غير موجود")
            all_exist = False
    
    return all_exist

def main():
    """الدالة الرئيسية"""
    print("=" * 70)
    print("🔧 اختبار الإصلاحات الجديدة - Testing New Fixes")
    print("=" * 70)
    
    # فحص الملفات
    if not check_files():
        print("\n❌ بعض الملفات مفقودة")
        return False
    
    # اختبار الاستيراد
    if not test_app_import():
        print("\n❌ فشل اختبار الاستيراد")
        return False
    
    # اختبار تحميل النموذج
    if not test_model_loading():
        print("\n❌ فشل اختبار تحميل النموذج")
        return False
    
    # اختبار دالة التحويل
    if not test_transcription():
        print("\n❌ فشل اختبار دالة التحويل")
        return False
    
    print("\n" + "=" * 70)
    print("🎉 جميع الاختبارات نجحت!")
    print("✅ الإصلاحات تعمل بشكل مثالي")
    print("⚡ النموذج يحمل عند الحاجة فقط")
    print("🛡️ لا توجد أخطاء في التحميل")
    print("=" * 70)
    
    print("\n🚀 الإصلاحات المطبقة:")
    print("   ✅ تحميل النموذج عند الحاجة فقط")
    print("   ✅ إصلاح مشكلة PyTorch 2.6 weights_only")
    print("   ✅ تحسين إعدادات Gradio launch")
    print("   ✅ معالجة أفضل للأخطاء")
    
    print("\n📁 التطبيق جاهز للرفع: huggingface_upload/")
    
    return True

if __name__ == "__main__":
    try:
        success = main()
        if not success:
            sys.exit(1)
    except KeyboardInterrupt:
        print("\n⏹️ تم إلغاء الاختبار")
    except Exception as e:
        print(f"\n❌ خطأ غير متوقع: {e}")
        sys.exit(1)
