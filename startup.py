#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ملف بدء التشغيل التلقائي لـ Hugging Face Spaces
Auto-startup file for Hugging Face Spaces
"""

import os
import sys
import time
import json
import secrets
from datetime import datetime

def generate_api_key():
    """إنشاء API Key فريد"""
    timestamp = int(time.time())
    random_part = secrets.token_urlsafe(16)
    api_key = f"whisper-{timestamp}-{random_part}"
    return api_key

def save_api_key(api_key):
    """حفظ API Key في ملف"""
    config = {
        "api_key": api_key,
        "created_at": datetime.now().isoformat(),
        "space_url": os.getenv('SPACE_ID', 'unknown'),
        "status": "active"
    }
    
    with open("api_config.json", "w") as f:
        json.dump(config, f, indent=2)
    
    print(f"✅ تم حفظ API Key في: api_config.json")

def display_startup_info(api_key):
    """عرض معلومات البدء"""
    space_id = os.getenv('SPACE_ID', 'YOUR_USERNAME-YOUR_SPACE_NAME')
    
    print("=" * 60)
    print("🚀 تم بدء تشغيل خدمة تحويل الفيديو/الصوت إلى نص")
    print("🚀 Video/Audio to Text Service Started")
    print("=" * 60)
    print()
    print(f"🔑 API Key: {api_key}")
    print(f"🌐 Space URL: https://huggingface.co/spaces/{space_id}")
    print(f"📡 API Endpoint: https://{space_id}.hf.space/api/predict")
    print()
    print("📋 استخدام API | API Usage:")
    print(f"   curl -X POST https://{space_id}.hf.space/api/predict \\")
    print(f"        -F 'data=@audio.mp3' \\")
    print(f"        -F 'data=[null,\"small\",\"Arabic\",\"transcribe\",\"{api_key}\"]'")
    print()
    print("🎯 النماذج المتاحة | Available Models:")
    
    models_info = {
        "tiny": "سريع جداً، دقة جيدة | Very fast, good accuracy",
        "base": "سريع، دقة أفضل | Fast, better accuracy", 
        "small": "متوازن، دقة عالية | Balanced, high accuracy"
    }
    
    for model, desc in models_info.items():
        model_path = f"./models/{model}.pt"
        status = "✅ متاح" if os.path.exists(model_path) else "❌ غير متاح"
        print(f"   - {model}: {status} - {desc}")
    
    print()
    print("=" * 60)

def check_models():
    """فحص النماذج المتاحة"""
    models_dir = "./models"
    if not os.path.exists(models_dir):
        print("⚠️ مجلد models غير موجود")
        return False
    
    required_models = ["tiny.pt", "base.pt", "small.pt"]
    available_models = []
    
    for model in required_models:
        model_path = os.path.join(models_dir, model)
        if os.path.exists(model_path):
            size_mb = os.path.getsize(model_path) / (1024 * 1024)
            available_models.append(f"{model} ({size_mb:.1f}MB)")
    
    if available_models:
        print(f"📦 النماذج المتاحة: {', '.join(available_models)}")
        return True
    else:
        print("❌ لا توجد نماذج متاحة")
        return False

def main():
    """الدالة الرئيسية"""
    print("🔄 بدء تشغيل النظام...")
    
    # فحص النماذج
    models_available = check_models()
    
    # إنشاء API Key
    api_key = generate_api_key()
    save_api_key(api_key)
    
    # عرض معلومات البدء
    display_startup_info(api_key)
    
    # إنشاء متغير بيئة للـ API Key
    os.environ['WHISPER_API_KEY'] = api_key
    
    if models_available:
        print("✅ النظام جاهز للعمل!")
    else:
        print("⚠️ النظام يعمل ولكن بدون نماذج محلية")
    
    return api_key

if __name__ == "__main__":
    main()
