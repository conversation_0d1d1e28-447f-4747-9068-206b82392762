#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار تطبيق Hugging Face قبل الرفع
Test Hugging Face app before upload
"""

import sys
import os

def test_imports():
    """اختبار الاستيراد"""
    print("🧪 اختبار الاستيراد...")
    
    try:
        import gradio as gr
        print(f"✅ Gradio: {gr.__version__}")
    except ImportError as e:
        print(f"❌ Gradio: {e}")
        return False
    
    try:
        import whisper
        print("✅ Whisper: مثبت")
    except ImportError as e:
        print(f"❌ Whisper: {e}")
        return False
    
    try:
        import torch
        print(f"✅ PyTorch: {torch.__version__}")
    except ImportError as e:
        print(f"❌ PyTorch: {e}")
        return False
    
    return True

def test_app_loading():
    """اختبار تحميل التطبيق"""
    print("\n🔄 اختبار تحميل التطبيق...")
    
    try:
        # استيراد التطبيق
        sys.path.insert(0, '.')
        import app_hf
        print("✅ تم استيراد app_hf بنجاح")
        
        # فحص المتغيرات المهمة
        if hasattr(app_hf, 'API_KEY'):
            print(f"✅ API Key: {app_hf.API_KEY}")
        else:
            print("❌ API Key غير موجود")
            return False
        
        if hasattr(app_hf, 'app'):
            print("✅ تطبيق Gradio موجود")
        else:
            print("❌ تطبيق Gradio غير موجود")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في تحميل التطبيق: {e}")
        return False

def test_functions():
    """اختبار الدوال"""
    print("\n🔧 اختبار الدوال...")
    
    try:
        import app_hf
        
        # اختبار دالة تحميل النموذج
        if hasattr(app_hf, 'load_model'):
            print("✅ دالة load_model موجودة")
        else:
            print("❌ دالة load_model غير موجودة")
            return False
        
        # اختبار دالة التحويل
        if hasattr(app_hf, 'transcribe_audio'):
            print("✅ دالة transcribe_audio موجودة")
        else:
            print("❌ دالة transcribe_audio غير موجودة")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار الدوال: {e}")
        return False

def run_quick_test():
    """تشغيل اختبار سريع"""
    print("\n🚀 تشغيل اختبار سريع...")
    
    try:
        import app_hf
        
        # اختبار دالة التحويل مع ملف فارغ
        result = app_hf.transcribe_audio(None, "Arabic")
        
        if isinstance(result, tuple) and len(result) == 3:
            text, info, api_info = result
            if "يرجى رفع ملف صوتي" in text:
                print("✅ دالة التحويل تعمل بشكل صحيح")
                return True
        
        print("❌ دالة التحويل لا تعمل كما متوقع")
        return False
        
    except Exception as e:
        print(f"❌ خطأ في الاختبار السريع: {e}")
        return False

def main():
    """الدالة الرئيسية"""
    print("=" * 60)
    print("🧪 اختبار تطبيق Hugging Face Spaces")
    print("=" * 60)
    
    # اختبار الاستيراد
    if not test_imports():
        print("\n❌ فشل اختبار الاستيراد")
        return False
    
    # اختبار تحميل التطبيق
    if not test_app_loading():
        print("\n❌ فشل اختبار تحميل التطبيق")
        return False
    
    # اختبار الدوال
    if not test_functions():
        print("\n❌ فشل اختبار الدوال")
        return False
    
    # اختبار سريع
    if not run_quick_test():
        print("\n❌ فشل الاختبار السريع")
        return False
    
    print("\n" + "=" * 60)
    print("✅ جميع الاختبارات نجحت!")
    print("🚀 التطبيق جاهز للرفع على Hugging Face Spaces")
    print("=" * 60)
    
    # عرض معلومات مهمة
    try:
        import app_hf
        print(f"\n🔑 API Key: {app_hf.API_KEY}")
        print("📁 الملفات المطلوبة للرفع:")
        print("   - app_hf.py (غير الاسم إلى app.py)")
        print("   - requirements.txt")
        print("   - README_HF.md (انسخ المحتوى إلى README.md)")
        print("\n📖 راجع HUGGING_FACE_UPLOAD_GUIDE.md للتفاصيل")
    except:
        pass
    
    return True

if __name__ == "__main__":
    try:
        success = main()
        if not success:
            sys.exit(1)
    except KeyboardInterrupt:
        print("\n⏹️ تم إلغاء الاختبار")
    except Exception as e:
        print(f"\n❌ خطأ غير متوقع: {e}")
        sys.exit(1)
