#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
محول الصوت إلى نص - Hugging Face Spaces
Audio to Text Converter for Hugging Face Spaces
مع إصلاح جميع الأخطاء الشائعة
"""

import gradio as gr
import whisper
import os
import secrets
from datetime import datetime
import warnings

# تجاهل التحذيرات غير المهمة
warnings.filterwarnings("ignore")

# إنشاء API Key فريد
API_KEY = f"whisper-small-{secrets.token_hex(8)}"

# متغير النموذج العام
model = None

def load_whisper_model():
    """تحميل نموذج Whisper Small مع معالجة الأخطاء"""
    global model
    
    if model is not None:
        return model
    
    try:
        print("🔄 تحميل نموذج Whisper Small...")
        
        # محاولة تحميل النموذج المحلي أولاً
        local_model_path = "./models/small.pt"
        if os.path.exists(local_model_path):
            print("📁 تحميل النموذج المحلي...")
            model = whisper.load_model(local_model_path)
            print("✅ تم تحميل النموذج المحلي بنجاح")
        else:
            print("🌐 تحميل نموذج Small من الإنترنت...")
            model = whisper.load_model("small")
            print("✅ تم تحميل نموذج Small بنجاح")
        
        return model
        
    except Exception as e:
        print(f"⚠️ خطأ في تحميل نموذج Small: {e}")
        print("🔄 محاولة تحميل نموذج Base كبديل...")
        try:
            model = whisper.load_model("base")
            print("✅ تم تحميل نموذج Base كبديل")
            return model
        except Exception as e2:
            print(f"⚠️ خطأ في تحميل نموذج Base: {e2}")
            print("🔄 محاولة تحميل نموذج Tiny كبديل أخير...")
            try:
                model = whisper.load_model("tiny")
                print("✅ تم تحميل نموذج Tiny كبديل أخير")
                return model
            except Exception as e3:
                print(f"❌ فشل تحميل جميع النماذج: {e3}")
                raise e3

def transcribe_audio(audio_file, language="Arabic"):
    """تحويل الصوت إلى نص مع معالجة شاملة للأخطاء"""
    
    # التحقق من وجود الملف
    if audio_file is None:
        return "❌ يرجى رفع ملف صوتي أو فيديو", "", ""
    
    try:
        # تحميل النموذج
        whisper_model = load_whisper_model()
        
        print(f"🎤 معالجة الملف: {audio_file}")
        
        # تحديد اللغة بشكل آمن
        language_codes = {
            "تلقائي": None,
            "Arabic": "ar",
            "English": "en", 
            "French": "fr",
            "Spanish": "es",
            "German": "de"
        }
        
        lang_code = language_codes.get(language, "ar")
        
        # تحويل الصوت
        if lang_code is None:
            result = whisper_model.transcribe(audio_file)
        else:
            result = whisper_model.transcribe(audio_file, language=lang_code)
        
        # استخراج النتائج بشكل آمن
        text = result.get("text", "").strip()
        detected_language = result.get("language", "غير محدد")
        
        # إحصائيات
        word_count = len(text.split()) if text else 0
        char_count = len(text) if text else 0
        
        # معلومات التحويل
        info = f"""📊 معلومات التحويل:
🤖 النموذج: Whisper Small
🌍 اللغة المكتشفة: {detected_language}
📝 عدد الكلمات: {word_count}
🔤 عدد الأحرف: {char_count}
🕒 الوقت: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}"""

        # معلومات API
        api_info = f"""🔑 API Key الخاص بك:
{API_KEY}

📡 استخدام API:
POST /api/predict
Content-Type: multipart/form-data

مثال Python:
import requests
files = {{"data": open("audio.mp3", "rb")}}
data = {{"data": [None, "Arabic"]}}
response = requests.post("API_URL", files=files, json=data)"""
        
        print(f"✅ تم التحويل بنجاح - {word_count} كلمة")
        return text, info, api_info
        
    except Exception as e:
        error_msg = f"❌ خطأ في التحويل: {str(e)}"
        print(error_msg)
        return error_msg, f"تفاصيل الخطأ: {str(e)}", ""

# تحميل النموذج عند بدء التطبيق
print("🚀 بدء تشغيل تطبيق Hugging Face Spaces...")
print(f"🔑 API Key: {API_KEY}")

# إنشاء واجهة Gradio مع إعدادات آمنة
with gr.Blocks(
    title="محول الصوت إلى نص - Whisper Small",
    theme=gr.themes.Soft(),
    css="""
    .gradio-container {
        max-width: 1200px !important;
        margin: auto !important;
    }
    .main-header {
        text-align: center;
        padding: 20px;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        border-radius: 15px;
        margin: 20px 0;
    }
    """
) as app:
    
    # العنوان الرئيسي
    gr.HTML("""
    <div class="main-header">
        <h1>🎤 محول الصوت إلى نص</h1>
        <h2>Audio to Text Converter</h2>
        <p>مدعوم بنموذج Whisper Small | Powered by Whisper Small Model</p>
        <p>دقة عالية للغة العربية والإنجليزية | High accuracy for Arabic & English</p>
    </div>
    """)
    
    # تبويب التحويل الرئيسي
    with gr.Tab("🎤 تحويل الصوت | Audio Conversion"):
        with gr.Row():
            with gr.Column(scale=1):
                audio_input = gr.Audio(
                    label="📁 ارفع ملف صوتي أو فيديو | Upload Audio/Video File",
                    type="filepath",
                    sources=["upload", "microphone"]
                )
                
                language_choice = gr.Dropdown(
                    choices=["تلقائي", "Arabic", "English", "French", "Spanish", "German"],
                    value="Arabic",
                    label="🌍 اللغة | Language"
                )
                
                convert_btn = gr.Button(
                    "🚀 تحويل إلى نص | Convert to Text", 
                    variant="primary",
                    size="lg"
                )
            
            with gr.Column(scale=2):
                output_text = gr.Textbox(
                    label="📝 النص المستخرج | Extracted Text",
                    lines=12,
                    max_lines=20,
                    show_copy_button=True,
                    placeholder="سيظهر النص المستخرج هنا..."
                )
        
        with gr.Row():
            info_output = gr.Textbox(
                label="📊 معلومات التحويل | Conversion Info",
                lines=6,
                show_copy_button=True,
                placeholder="ستظهر معلومات التحويل هنا..."
            )
            
            api_output = gr.Textbox(
                label="🔑 معلومات API | API Information",
                lines=6,
                show_copy_button=True,
                placeholder="ستظهر معلومات API هنا..."
            )
    
    # تبويب API Key والاستخدام
    with gr.Tab("🔑 API Key & Usage"):
        gr.HTML(f"""
        <div style="background: #f8f9fa; padding: 20px; border-radius: 10px; margin: 10px 0;">
            <h3>🔐 API Key الخاص بك:</h3>
            <div style="background: #e9ecef; padding: 15px; border-radius: 5px; font-family: monospace; font-size: 16px; word-break: break-all; border: 2px solid #007bff;">
                {API_KEY}
            </div>
            <p style="color: #6c757d; margin-top: 10px;">احتفظ بهذا المفتاح في مكان آمن</p>
        </div>
        """)
        
        gr.Markdown("""
### 📡 كيفية استخدام API:

#### Python Example:
```python
import requests
import json

# استبدل بالرابط الحقيقي لـ Space الخاص بك
API_URL = "https://YOUR_USERNAME-YOUR_SPACE_NAME.hf.space/api/predict"

# رفع ملف وتحويله
with open("audio.mp3", "rb") as f:
    files = {"data": f}
    data = {"data": [None, "Arabic"]}
    
response = requests.post(API_URL, files=files, json=data)
result = response.json()

print("النص:", result["data"][0])
print("المعلومات:", result["data"][1])
```

#### cURL Example:
```bash
curl -X POST \\
  -F "data=@audio.mp3" \\
  -F "data=Arabic" \\
  https://YOUR_USERNAME-YOUR_SPACE_NAME.hf.space/api/predict
```

### 🔒 ملاحظات أمنية:
- احتفظ بـ API Key في مكان آمن
- لا تشارك API Key مع أحد
- استخدم HTTPS دائماً
- API Key يتغير مع كل إعادة تشغيل للتطبيق
        """)
    
    # تبويب المعلومات
    with gr.Tab("ℹ️ معلومات | Information"):
        gr.Markdown("""
### 🤖 حول النموذج:
- **النموذج**: OpenAI Whisper Small
- **الحجم**: ~244 MB
- **اللغات المدعومة**: 99+ لغة
- **الدقة**: عالية جداً للغة العربية والإنجليزية

### 📋 الملفات المدعومة:
- **الصوت**: MP3, WAV, M4A, FLAC, OGG
- **الفيديو**: MP4, AVI, MOV, MKV
- **الحد الأقصى**: 25 MB لكل ملف

### ⚡ الميزات:
- تحويل سريع ودقيق
- دعم اللغة العربية بدقة عالية
- API للاستخدام البرمجي
- واجهة سهلة الاستخدام
- مجاني تماماً

### 🔧 التطوير:
- **التقنية**: Python + Gradio + Whisper
- **الاستضافة**: Hugging Face Spaces
- **مفتوح المصدر**: متاح للجميع
        """)
    
    # ربط الأحداث
    convert_btn.click(
        fn=transcribe_audio,
        inputs=[audio_input, language_choice],
        outputs=[output_text, info_output, api_output]
    )

# تشغيل التطبيق مع إعدادات آمنة لـ Hugging Face Spaces
if __name__ == "__main__":
    print(f"\n🎉 التطبيق جاهز للنشر على Hugging Face Spaces!")
    print(f"🔑 API Key: {API_KEY}")
    
    # تحميل النموذج مسبقاً
    try:
        load_whisper_model()
        print("✅ النموذج محمل ومستعد")
    except Exception as e:
        print(f"⚠️ تحذير: {e}")
    
    # تشغيل التطبيق بإعدادات آمنة لـ Hugging Face
    # لا نحدد server_name أو server_port لتجنب الأخطاء
    app.launch(
        share=False,
        debug=False,
        show_error=True,
        quiet=False
    )
