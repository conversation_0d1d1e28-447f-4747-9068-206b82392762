#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار نهائي للتطبيق قبل الرفع
Final test before upload
"""

import os
import sys

def test_files():
    """فحص الملفات المطلوبة"""
    print("📁 فحص الملفات...")
    
    required_files = {
        "README.md": "وصف المشروع",
        "app.py": "التطبيق المبسط",
        "requirements.txt": "المكتبات المحدثة"
    }
    
    all_good = True
    
    for file, desc in required_files.items():
        if os.path.exists(file):
            size = os.path.getsize(file)
            print(f"   ✅ {file} ({size} bytes) - {desc}")
        else:
            print(f"   ❌ {file} مفقود - {desc}")
            all_good = False
    
    return all_good

def test_requirements():
    """فحص requirements.txt"""
    print("\n📦 فحص requirements.txt...")
    
    if not os.path.exists("requirements.txt"):
        print("   ❌ ملف requirements.txt مفقود")
        return False
    
    with open("requirements.txt", "r") as f:
        content = f.read()
    
    required_packages = [
        "openai-whisper",
        "gradio==4.44.1",
        "torch",
        "torchaudio",
        "numpy"
    ]
    
    print("   المكتبات المطلوبة:")
    all_good = True
    
    for package in required_packages:
        if package in content:
            print(f"   ✅ {package}")
        else:
            print(f"   ❌ {package} مفقود")
            all_good = False
    
    return all_good

def test_app_import():
    """اختبار استيراد التطبيق"""
    print("\n🎤 اختبار التطبيق...")
    
    try:
        # محاولة استيراد التطبيق
        import app
        print("   ✅ تم استيراد التطبيق بنجاح")
        
        # فحص المتغيرات المهمة
        if hasattr(app, 'API_KEY'):
            print(f"   ✅ API Key: {app.API_KEY}")
        else:
            print("   ❌ API Key غير موجود")
            return False
        
        if hasattr(app, 'app'):
            print("   ✅ واجهة Gradio موجودة")
        else:
            print("   ❌ واجهة Gradio غير موجودة")
            return False
        
        return True
        
    except Exception as e:
        print(f"   ❌ خطأ في استيراد التطبيق: {e}")
        return False

def test_models():
    """فحص النماذج المحلية"""
    print("\n🤖 فحص النماذج...")
    
    models_dir = "./models"
    if not os.path.exists(models_dir):
        print("   ⚠️ مجلد models غير موجود (سيتم التحميل من الإنترنت)")
        return True
    
    models = ["tiny.pt", "base.pt", "small.pt"]
    found_models = []
    
    for model in models:
        model_path = os.path.join(models_dir, model)
        if os.path.exists(model_path):
            size_mb = os.path.getsize(model_path) / (1024 * 1024)
            print(f"   ✅ {model} ({size_mb:.1f}MB)")
            found_models.append(model)
        else:
            print(f"   ❌ {model} غير موجود")
    
    if found_models:
        print(f"   📊 تم العثور على {len(found_models)} نموذج محلي")
        return True
    else:
        print("   ⚠️ لا توجد نماذج محلية (سيتم التحميل من الإنترنت)")
        return True

def main():
    """الدالة الرئيسية"""
    print("🧪 اختبار نهائي قبل الرفع على Hugging Face")
    print("=" * 60)
    
    tests = [
        ("فحص الملفات", test_files),
        ("فحص المكتبات", test_requirements),
        ("اختبار التطبيق", test_app_import),
        ("فحص النماذج", test_models)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"   ❌ خطأ في {test_name}: {e}")
            results.append((test_name, False))
    
    # النتيجة النهائية
    print("\n" + "=" * 60)
    print("📋 ملخص النتائج:")
    print("=" * 60)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ نجح" if result else "❌ فشل"
        print(f"   {status} - {test_name}")
        if result:
            passed += 1
    
    print(f"\n📊 النتيجة: {passed}/{total} اختبار نجح")
    
    if passed >= 3:  # على الأقل 3 من 4 اختبارات
        print("\n🎉 ممتاز! التطبيق جاهز للرفع")
        print("✅ يمكنك رفع الملفات على Hugging Face الآن")
        print("\n🚀 الملفات المطلوبة:")
        print("   - README.md")
        print("   - app.py")
        print("   - requirements.txt")
        print("   - models/ (اختياري)")
        print("\n🔑 API Key: whisper-personal-2025")
        print("📡 سيكون متاح على: https://YOUR-SPACE-URL.hf.space")
    else:
        print(f"\n⚠️ يجب إصلاح {total - passed} مشكلة قبل الرفع")

if __name__ == "__main__":
    main()
