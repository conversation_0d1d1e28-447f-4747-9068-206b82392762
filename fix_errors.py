#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
إصلاح الأخطاء الشائعة في التطبيق
Fix common application errors
"""

import os
import sys
import subprocess
import psutil

def kill_gradio_processes():
    """إنهاء عمليات Gradio المعلقة"""
    print("🔍 البحث عن عمليات Gradio معلقة...")
    
    killed = 0
    for proc in psutil.process_iter(['pid', 'name', 'cmdline']):
        try:
            cmdline = ' '.join(proc.info['cmdline'] or [])
            if 'gradio' in cmdline.lower() or 'app.py' in cmdline:
                print(f"⚠️ إنهاء العملية: {proc.info['pid']} - {proc.info['name']}")
                proc.kill()
                killed += 1
        except (psutil.NoSuchProcess, psutil.AccessDenied):
            pass
    
    if killed > 0:
        print(f"✅ تم إنهاء {killed} عملية")
    else:
        print("✅ لا توجد عمليات معلقة")

def check_port(port=7860):
    """فحص المنفذ"""
    print(f"🔍 فحص المنفذ {port}...")
    
    for conn in psutil.net_connections():
        if conn.laddr.port == port:
            print(f"⚠️ المنفذ {port} مستخدم بواسطة العملية {conn.pid}")
            return False
    
    print(f"✅ المنفذ {port} متاح")
    return True

def clear_cache():
    """مسح الذاكرة المؤقتة"""
    print("🧹 مسح الذاكرة المؤقتة...")
    
    # مسح __pycache__
    for root, dirs, files in os.walk('.'):
        for dir_name in dirs:
            if dir_name == '__pycache__':
                cache_path = os.path.join(root, dir_name)
                try:
                    import shutil
                    shutil.rmtree(cache_path)
                    print(f"🗑️ تم مسح: {cache_path}")
                except Exception as e:
                    print(f"❌ فشل مسح {cache_path}: {e}")
    
    # مسح ملفات .pyc
    for root, dirs, files in os.walk('.'):
        for file_name in files:
            if file_name.endswith('.pyc'):
                file_path = os.path.join(root, file_name)
                try:
                    os.remove(file_path)
                    print(f"🗑️ تم مسح: {file_path}")
                except Exception as e:
                    print(f"❌ فشل مسح {file_path}: {e}")

def fix_gradio_config():
    """إصلاح إعدادات Gradio"""
    print("🔧 إصلاح إعدادات Gradio...")
    
    # إنشاء ملف إعدادات Gradio
    gradio_config = """
import os
os.environ['GRADIO_SERVER_NAME'] = '127.0.0.1'
os.environ['GRADIO_SERVER_PORT'] = '7860'
os.environ['GRADIO_SHARE'] = 'False'
os.environ['GRADIO_DEBUG'] = 'False'
"""
    
    try:
        with open('gradio_config.py', 'w', encoding='utf-8') as f:
            f.write(gradio_config)
        print("✅ تم إنشاء ملف إعدادات Gradio")
    except Exception as e:
        print(f"❌ فشل إنشاء ملف الإعدادات: {e}")

def test_imports():
    """اختبار الاستيراد"""
    print("🧪 اختبار الاستيراد...")
    
    modules = ['gradio', 'whisper', 'torch', 'numpy']
    
    for module in modules:
        try:
            __import__(module)
            print(f"✅ {module}")
        except ImportError as e:
            print(f"❌ {module}: {e}")

def create_minimal_app():
    """إنشاء تطبيق مبسط للاختبار"""
    print("🔧 إنشاء تطبيق اختبار مبسط...")
    
    minimal_app = '''#!/usr/bin/env python3
import gradio as gr

def hello(name):
    return f"مرحبا {name}!"

with gr.Blocks() as app:
    gr.HTML("<h1>اختبار Gradio</h1>")
    
    with gr.Row():
        name_input = gr.Textbox(label="الاسم")
        output = gr.Textbox(label="الرد")
    
    btn = gr.Button("اختبار")
    btn.click(hello, inputs=[name_input], outputs=[output])

if __name__ == "__main__":
    try:
        app.launch(
            server_name="127.0.0.1",
            server_port=7860,
            share=False,
            debug=False
        )
    except Exception as e:
        print(f"خطأ: {e}")
'''
    
    try:
        with open('test_app.py', 'w', encoding='utf-8') as f:
            f.write(minimal_app)
        print("✅ تم إنشاء test_app.py")
    except Exception as e:
        print(f"❌ فشل إنشاء التطبيق: {e}")

def main():
    """الدالة الرئيسية"""
    print("=" * 50)
    print("🔧 إصلاح أخطاء التطبيق")
    print("=" * 50)
    
    try:
        # إنهاء العمليات المعلقة
        kill_gradio_processes()
        
        # فحص المنفذ
        check_port()
        
        # مسح الذاكرة المؤقتة
        clear_cache()
        
        # إصلاح إعدادات Gradio
        fix_gradio_config()
        
        # اختبار الاستيراد
        test_imports()
        
        # إنشاء تطبيق اختبار
        create_minimal_app()
        
        print("\n" + "=" * 50)
        print("✅ تم الانتهاء من الإصلاحات")
        print("💡 جرب الآن:")
        print("  - python test_app.py (للاختبار)")
        print("  - python run_app.py (للتطبيق الكامل)")
        print("=" * 50)
        
    except Exception as e:
        print(f"❌ خطأ في الإصلاح: {e}")

if __name__ == "__main__":
    main()
