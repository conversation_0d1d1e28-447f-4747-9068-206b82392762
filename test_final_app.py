#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار التطبيق النهائي للتأكد من عدم ظهور الأخطاء
Final app test to ensure no errors occur
"""

import sys
import os
import subprocess
import time

def test_app_import():
    """اختبار استيراد التطبيق"""
    print("🧪 اختبار استيراد التطبيق...")
    
    try:
        # إضافة مجلد huggingface_upload إلى المسار
        sys.path.insert(0, './huggingface_upload')
        
        # استيراد التطبيق
        import app
        
        print("✅ تم استيراد التطبيق بنجاح")
        
        # فحص المتغيرات المهمة
        if hasattr(app, 'API_KEY'):
            print(f"✅ API Key: {app.API_KEY}")
        
        if hasattr(app, 'app'):
            print("✅ تطبيق Gradio موجود")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في استيراد التطبيق: {e}")
        return False

def test_functions():
    """اختبار الدوال الأساسية"""
    print("\n🔧 اختبار الدوال...")
    
    try:
        sys.path.insert(0, './huggingface_upload')
        import app
        
        # اختبار دالة التحويل مع ملف فارغ
        result = app.transcribe_audio(None, "Arabic")
        
        if isinstance(result, tuple) and len(result) == 3:
            text, info, api_info = result
            if "يرجى رفع ملف صوتي" in text:
                print("✅ دالة transcribe_audio تعمل بشكل صحيح")
                return True
        
        print("❌ دالة transcribe_audio لا تعمل كما متوقع")
        return False
        
    except Exception as e:
        print(f"❌ خطأ في اختبار الدوال: {e}")
        return False

def test_gradio_launch():
    """اختبار تشغيل Gradio بدون أخطاء"""
    print("\n🚀 اختبار تشغيل Gradio...")
    
    try:
        # إنشاء ملف اختبار مؤقت
        test_code = '''
import sys
sys.path.insert(0, './huggingface_upload')
import app

# محاولة تشغيل التطبيق لثواني قليلة
import threading
import time

def stop_app():
    time.sleep(5)
    import os
    os._exit(0)

# تشغيل مؤقت للإيقاف
timer = threading.Thread(target=stop_app)
timer.daemon = True
timer.start()

# تشغيل التطبيق
app.app.launch(share=False, debug=False, show_error=True, quiet=True)
'''
        
        with open('test_launch.py', 'w', encoding='utf-8') as f:
            f.write(test_code)
        
        # تشغيل الاختبار
        process = subprocess.Popen([
            sys.executable, 'test_launch.py'
        ], stdout=subprocess.PIPE, stderr=subprocess.PIPE, text=True)
        
        # انتظار لمدة 10 ثواني
        try:
            stdout, stderr = process.communicate(timeout=10)
            
            # فحص الأخطاء المحددة
            error_patterns = [
                "TypeError: argument of type 'bool' is not iterable",
                "ValueError: When localhost is not accessible, a shareable link must be created"
            ]
            
            has_target_errors = any(pattern in stderr for pattern in error_patterns)
            
            if has_target_errors:
                print("❌ ظهرت الأخطاء المستهدفة:")
                for pattern in error_patterns:
                    if pattern in stderr:
                        print(f"   - {pattern}")
                return False
            else:
                print("✅ لم تظهر الأخطاء المستهدفة")
                return True
                
        except subprocess.TimeoutExpired:
            process.kill()
            print("✅ التطبيق يعمل بدون أخطاء (انتهت المهلة الزمنية)")
            return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار Gradio: {e}")
        return False
    
    finally:
        # تنظيف الملف المؤقت
        if os.path.exists('test_launch.py'):
            os.remove('test_launch.py')

def check_files():
    """فحص وجود الملفات المطلوبة"""
    print("\n📁 فحص الملفات...")
    
    required_files = [
        'huggingface_upload/app.py',
        'huggingface_upload/requirements.txt',
        'huggingface_upload/README.md',
        'huggingface_upload/.gitignore',
        'huggingface_upload/UPLOAD_INSTRUCTIONS.md'
    ]
    
    all_exist = True
    for file_path in required_files:
        if os.path.exists(file_path):
            print(f"✅ {file_path}")
        else:
            print(f"❌ {file_path} غير موجود")
            all_exist = False
    
    return all_exist

def main():
    """الدالة الرئيسية"""
    print("=" * 70)
    print("🧪 اختبار التطبيق النهائي - Final App Test")
    print("=" * 70)
    
    # فحص الملفات
    if not check_files():
        print("\n❌ بعض الملفات مفقودة")
        return False
    
    # اختبار الاستيراد
    if not test_app_import():
        print("\n❌ فشل اختبار الاستيراد")
        return False
    
    # اختبار الدوال
    if not test_functions():
        print("\n❌ فشل اختبار الدوال")
        return False
    
    # اختبار تشغيل Gradio
    if not test_gradio_launch():
        print("\n❌ فشل اختبار تشغيل Gradio")
        return False
    
    print("\n" + "=" * 70)
    print("🎉 جميع الاختبارات نجحت!")
    print("✅ التطبيق جاهز للرفع على Hugging Face Spaces")
    print("🔧 تم إصلاح جميع الأخطاء المستهدفة")
    print("=" * 70)
    
    print("\n📁 الملفات الجاهزة في مجلد: huggingface_upload/")
    print("📖 راجع UPLOAD_INSTRUCTIONS.md للتفاصيل")
    
    return True

if __name__ == "__main__":
    try:
        success = main()
        if not success:
            sys.exit(1)
    except KeyboardInterrupt:
        print("\n⏹️ تم إلغاء الاختبار")
    except Exception as e:
        print(f"\n❌ خطأ غير متوقع: {e}")
        sys.exit(1)
