# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# Gradio
gradio_cached_examples/
flagged/

# Audio/Video files
*.mp3
*.wav
*.mp4
*.avi
*.mov
*.mkv
*.m4a
*.flac
*.ogg

# Temporary files
*.tmp
*.temp
temp/
tmp/

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# IDE
.vscode/
.idea/
*.swp
*.swo
*~

# Logs
*.log
logs/

# Backup files
*.bak
*.backup

# Models (keep embedded models)
# models/small.pt is included for embedded model
