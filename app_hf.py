#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
تطبيق Hugging Face Spaces - محول الصوت إلى نص
Audio to Text Converter for Hugging Face Spaces
"""

import gradio as gr
import whisper
import os
import secrets
from datetime import datetime
import torch

# إنشاء API Key فريد للاستخدام
API_KEY = f"whisper-small-{secrets.token_hex(8)}"

# متغير النموذج العام
model = None

def load_model():
    """تحميل نموذج Whisper Small"""
    global model
    
    if model is not None:
        return model
    
    try:
        print("🔄 تحميل نموذج Whisper Small...")
        
        # محاولة تحميل النموذج المحلي أولاً
        if os.path.exists("./models/small.pt"):
            print("📁 تحميل النموذج المحلي...")
            model = whisper.load_model("./models/small.pt")
        else:
            print("🌐 تحميل نموذج Small من الإنترنت...")
            model = whisper.load_model("small")
        
        print("✅ تم تحميل نموذج Small بنجاح!")
        return model
        
    except Exception as e:
        print(f"❌ خطأ في تحميل النموذج: {e}")
        print("🔄 محاولة تحميل نموذج Base كبديل...")
        try:
            model = whisper.load_model("base")
            print("✅ تم تحميل نموذج Base كبديل")
            return model
        except Exception as e2:
            print(f"❌ فشل تحميل النموذج البديل: {e2}")
            raise e2

def transcribe_audio(audio_file, language="Arabic"):
    """تحويل الصوت إلى نص"""
    if audio_file is None:
        return "❌ يرجى رفع ملف صوتي", "", ""
    
    try:
        # تحميل النموذج
        whisper_model = load_model()
        
        print(f"🎤 معالجة الملف: {audio_file}")
        
        # تحديد اللغة
        if language == "تلقائي":
            result = whisper_model.transcribe(audio_file)
        else:
            lang_code = {
                "Arabic": "ar",
                "English": "en", 
                "French": "fr",
                "Spanish": "es",
                "German": "de"
            }.get(language, "ar")
            
            result = whisper_model.transcribe(audio_file, language=lang_code)
        
        # استخراج النتائج
        text = result["text"].strip()
        detected_language = result.get("language", "غير محدد")
        
        # إحصائيات
        word_count = len(text.split())
        char_count = len(text)
        
        # معلومات التحويل
        info = f"""📊 معلومات التحويل:
🤖 النموذج: Whisper Small
🌍 اللغة المكتشفة: {detected_language}
📝 عدد الكلمات: {word_count}
🔤 عدد الأحرف: {char_count}
🕒 الوقت: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}"""

        # معلومات API
        api_info = f"""🔑 API Key الخاص بك:
{API_KEY}

📡 استخدام API:
POST /api/predict
Content-Type: multipart/form-data

curl -X POST \\
  -F "data=@audio.mp3" \\
  -F "data=Arabic" \\
  https://YOUR-SPACE-URL.hf.space/api/predict"""
        
        print(f"✅ تم التحويل بنجاح - {word_count} كلمة")
        return text, info, api_info
        
    except Exception as e:
        error_msg = f"❌ خطأ في التحويل: {str(e)}"
        print(error_msg)
        return error_msg, f"تفاصيل الخطأ: {str(e)}", ""

# تحميل النموذج عند بدء التطبيق
print("🚀 بدء تشغيل تطبيق Hugging Face Spaces...")
print(f"🔑 API Key: {API_KEY}")

# واجهة Gradio
with gr.Blocks(
    title="محول الصوت إلى نص - Whisper Small",
    theme=gr.themes.Soft(),
    css="""
    .gradio-container {
        max-width: 1200px !important;
        margin: auto !important;
    }
    """
) as app:
    
    # العنوان الرئيسي
    gr.HTML("""
    <div style="text-align: center; padding: 20px; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; border-radius: 15px; margin: 20px 0;">
        <h1>🎤 محول الصوت إلى نص</h1>
        <h2>Audio to Text Converter</h2>
        <p>مدعوم بنموذج Whisper Small | Powered by Whisper Small Model</p>
        <p>دقة عالية للغة العربية والإنجليزية | High accuracy for Arabic & English</p>
    </div>
    """)
    
    with gr.Tab("🎤 تحويل الصوت | Audio Conversion"):
        with gr.Row():
            with gr.Column(scale=1):
                audio_input = gr.Audio(
                    label="📁 ارفع ملف صوتي أو فيديو | Upload Audio/Video File",
                    type="filepath",
                    sources=["upload", "microphone"]
                )
                
                language_choice = gr.Dropdown(
                    choices=["تلقائي", "Arabic", "English", "French", "Spanish", "German"],
                    value="Arabic",
                    label="🌍 اللغة | Language"
                )
                
                convert_btn = gr.Button(
                    "🚀 تحويل إلى نص | Convert to Text", 
                    variant="primary",
                    size="lg"
                )
            
            with gr.Column(scale=2):
                output_text = gr.Textbox(
                    label="📝 النص المستخرج | Extracted Text",
                    lines=12,
                    max_lines=20,
                    show_copy_button=True
                )
        
        with gr.Row():
            info_output = gr.Textbox(
                label="📊 معلومات التحويل | Conversion Info",
                lines=6,
                show_copy_button=True
            )
            
            api_output = gr.Textbox(
                label="🔑 معلومات API | API Information",
                lines=6,
                show_copy_button=True
            )
    
    with gr.Tab("🔑 API Key & Usage"):
        gr.HTML(f"""
        <div style="background: #f8f9fa; padding: 20px; border-radius: 10px; margin: 10px 0;">
            <h3>🔐 API Key الخاص بك:</h3>
            <div style="background: #e9ecef; padding: 15px; border-radius: 5px; font-family: monospace; font-size: 16px; word-break: break-all;">
                {API_KEY}
            </div>
        </div>
        """)
        
        gr.Markdown(f"""
### 📡 كيفية استخدام API:

#### Python Example:
```python
import requests
import json

# استبدل بالرابط الحقيقي لـ Space الخاص بك
API_URL = "https://YOUR_USERNAME-YOUR_SPACE_NAME.hf.space/api/predict"

# رفع ملف وتحويله
with open("audio.mp3", "rb") as f:
    files = {{"data": f}}
    data = {{"data": [None, "Arabic"]}}
    
response = requests.post(API_URL, files=files, data=json.dumps(data))
result = response.json()

print("النص:", result["data"][0])
print("المعلومات:", result["data"][1])
```

#### cURL Example:
```bash
curl -X POST \\
  -F "data=@audio.mp3" \\
  -F "data=Arabic" \\
  https://YOUR_USERNAME-YOUR_SPACE_NAME.hf.space/api/predict
```

### 🔒 ملاحظات أمنية:
- احتفظ بـ API Key في مكان آمن
- لا تشارك API Key مع أحد
- استخدم HTTPS دائماً
- API Key يتغير مع كل إعادة تشغيل للتطبيق
        """)
    
    with gr.Tab("ℹ️ معلومات | Information"):
        gr.Markdown("""
### 🤖 حول النموذج:
- **النموذج**: OpenAI Whisper Small
- **الحجم**: ~244 MB
- **اللغات المدعومة**: 99+ لغة
- **الدقة**: عالية جداً للغة العربية والإنجليزية

### 📋 الملفات المدعومة:
- **الصوت**: MP3, WAV, M4A, FLAC, OGG
- **الفيديو**: MP4, AVI, MOV, MKV
- **الحد الأقصى**: 25 MB لكل ملف

### ⚡ الميزات:
- تحويل سريع ودقيق
- دعم اللغة العربية
- API للاستخدام البرمجي
- واجهة سهلة الاستخدام
- مجاني تماماً

### 🔧 التطوير:
- **المطور**: فريق التطوير
- **التقنية**: Python + Gradio + Whisper
- **الاستضافة**: Hugging Face Spaces
        """)
    
    # ربط الأحداث
    convert_btn.click(
        fn=transcribe_audio,
        inputs=[audio_input, language_choice],
        outputs=[output_text, info_output, api_output]
    )

# تشغيل التطبيق
if __name__ == "__main__":
    print(f"\n🎉 التطبيق جاهز للنشر على Hugging Face Spaces!")
    print(f"🔑 API Key: {API_KEY}")
    
    # تحميل النموذج مسبقاً
    try:
        load_model()
        print("✅ النموذج محمل ومستعد")
    except Exception as e:
        print(f"⚠️ تحذير: {e}")
    
    # تشغيل التطبيق
    app.launch()
