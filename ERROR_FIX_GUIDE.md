# 🔧 دليل إصلاح الأخطاء - Error Fix Guide

## 🚨 الأخطاء الشائعة وحلولها

### 1. خطأ `TypeError: argument of type 'bool' is not iterable`

**السبب:** مشكلة في إعدادات Gradio launch()

**الحل:**
```bash
python fix_errors.py
python run_app.py
```

### 2. خطأ `ValueError: When localhost is not accessible, a shareable link must be created`

**السبب:** مشكلة في إعدادات الشبكة

**الحل:**
```bash
# تشغيل مع إعدادات محددة
python app_simple.py
```

### 3. خطأ `Address already in use`

**السبب:** المنفذ 7860 مستخدم

**الحل:**
```bash
# إنهاء العمليات المعلقة
python fix_errors.py

# أو تغيير المنفذ يدوياً
python -c "
import app_simple
app_simple.app.launch(server_port=7861)
"
```

## 🛠️ خطوات الإصلاح السريع

### الخطوة 1: تشغيل أداة الإصلاح
```bash
python fix_errors.py
```

### الخطوة 2: اختبار التطبيق المبسط
```bash
python test_app.py
```

### الخطوة 3: تشغيل التطبيق الكامل
```bash
python run_app.py
```

## 📋 فحص المتطلبات

### فحص Python والمكتبات:
```bash
python --version
pip list | grep gradio
pip list | grep whisper
```

### فحص النموذج:
```bash
ls -la models/
```

## 🔄 إعادة التثبيت (إذا لزم الأمر)

```bash
# إلغاء تثبيت المكتبات
pip uninstall gradio whisper torch -y

# إعادة التثبيت
pip install -r requirements.txt
```

## 🚀 طرق التشغيل البديلة

### ⭐ الطريقة الموصى بها (الأسهل):
```bash
python start_app.py
```

### الطريقة 1: اختبار بسيط
```bash
python simple_test.py
```

### الطريقة 2: التطبيق المبسط
```bash
python app_simple.py
```

### الطريقة 3: التطبيق الكامل
```bash
python app.py
```

### الطريقة 4: التشغيل المحسن
```bash
python run_app.py
```

### الطريقة 5: التشغيل اليدوي
```python
import gradio as gr
import app_simple

app_simple.app.launch(
    server_name="127.0.0.1",
    server_port=7860,
    share=False,
    debug=False
)
```

## 🔍 تشخيص المشاكل

### فحص العمليات المعلقة:
```bash
# Windows
tasklist | findstr python
netstat -an | findstr 7860

# Linux/Mac
ps aux | grep python
netstat -an | grep 7860
```

### فحص السجلات:
```bash
python app_simple.py 2>&1 | tee app.log
```

## 📞 الحصول على المساعدة

إذا استمرت المشاكل:

1. **تشغيل التشخيص الكامل:**
   ```bash
   python fix_errors.py
   ```

2. **إرسال معلومات النظام:**
   ```bash
   python -c "
   import sys, platform
   print(f'Python: {sys.version}')
   print(f'Platform: {platform.platform()}')
   import gradio, whisper
   print(f'Gradio: {gradio.__version__}')
   print('Whisper: installed')
   "
   ```

3. **اختبار الاتصال:**
   ```bash
   curl http://127.0.0.1:7860
   ```

## ✅ التحقق من نجاح الإصلاح

عند نجاح التشغيل ستظهر الرسائل التالية:
```
✅ التطبيق جاهز للاستخدام الشخصي
🔑 API Key: whisper-personal-2025
🚀 بدء تشغيل الخادم على localhost:7860...
Running on local URL:  http://127.0.0.1:7860
```

## 🎯 نصائح مهمة

1. **استخدم دائماً `run_app.py` للتشغيل الآمن**
2. **تأكد من إغلاق التطبيق بـ Ctrl+C قبل إعادة التشغيل**
3. **في حالة المشاكل المستمرة، استخدم `test_app.py` للاختبار**
4. **احتفظ بنسخة احتياطية من النماذج في مجلد `models/`**

---

**آخر تحديث:** 2025-01-20
**الإصدار:** 2.0 - مع إصلاح الأخطاء الشائعة
