#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار بسيط جداً لـ Gradio
Very simple Gradio test
"""

import gradio as gr

def hello(name):
    if not name:
        return "مرحبا! أدخل اسمك"
    return f"مرحبا {name}! 🎉"

# إنشاء واجهة بسيطة
with gr.Blocks(title="اختبار Gradio") as app:
    gr.HTML("<h1 style='text-align: center;'>🧪 اختبار Gradio</h1>")
    
    with gr.Row():
        name_input = gr.Textbox(label="اكتب اسمك", placeholder="أدخل اسمك هنا...")
        output = gr.Textbox(label="الرد")
    
    btn = gr.Button("اختبار", variant="primary")
    btn.click(hello, inputs=[name_input], outputs=[output])

if __name__ == "__main__":
    print("🧪 اختبار Gradio البسيط...")
    
    try:
        app.launch(
            server_name="127.0.0.1",
            server_port=7860,
            share=False,
            debug=False,
            show_error=True,
            inbrowser=True
        )
    except Exception as e:
        print(f"❌ خطأ: {e}")
        print("🔄 محاولة بديلة...")
        try:
            app.launch()
        except Exception as e2:
            print(f"❌ فشل نهائي: {e2}")
