#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار إصلاحات Hugging Face Spaces
Test Hugging Face Spaces fixes
"""

import sys
import os
import subprocess
import time

def test_gradio_import():
    """اختبار استيراد Gradio"""
    print("🧪 اختبار استيراد Gradio...")
    
    try:
        import gradio as gr
        print(f"✅ Gradio: {gr.__version__}")
        return True
    except ImportError as e:
        print(f"❌ Gradio: {e}")
        return False

def test_app_import():
    """اختبار استيراد التطبيق"""
    print("\n🧪 اختبار استيراد التطبيق...")
    
    try:
        sys.path.insert(0, './huggingface_upload')
        import app
        
        print("✅ تم استيراد التطبيق بنجاح")
        
        # فحص المتغيرات
        if hasattr(app, 'API_KEY'):
            print(f"✅ API Key: {app.API_KEY}")
        
        if hasattr(app, 'app'):
            print("✅ تطبيق Gradio موجود")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في استيراد التطبيق: {e}")
        return False

def test_gradio_components():
    """اختبار مكونات Gradio"""
    print("\n🧪 اختبار مكونات Gradio...")
    
    try:
        import gradio as gr
        
        # اختبار إنشاء مكونات بسيطة
        audio = gr.Audio(label="Test", type="filepath")
        textbox = gr.Textbox(label="Test", lines=5)
        button = gr.Button("Test", variant="primary")
        dropdown = gr.Dropdown(choices=["A", "B"], value="A", label="Test")
        
        print("✅ جميع مكونات Gradio تعمل")
        return True
        
    except Exception as e:
        print(f"❌ خطأ في مكونات Gradio: {e}")
        return False

def test_launch_settings():
    """اختبار إعدادات launch"""
    print("\n🧪 اختبار إعدادات launch...")
    
    try:
        sys.path.insert(0, './huggingface_upload')
        import app
        
        # محاولة إنشاء تطبيق مؤقت للاختبار
        import gradio as gr
        
        with gr.Blocks() as test_app:
            gr.HTML("<h1>Test</h1>")
        
        # اختبار الإعدادات
        launch_kwargs = {
            'share': True,
            'debug': False
        }
        
        print("✅ إعدادات launch صحيحة")
        return True
        
    except Exception as e:
        print(f"❌ خطأ في إعدادات launch: {e}")
        return False

def test_model_loading():
    """اختبار تحميل النموذج"""
    print("\n🧪 اختبار تحميل النموذج...")
    
    try:
        sys.path.insert(0, './huggingface_upload')
        import app
        
        # اختبار تحميل النموذج
        model = app.load_whisper_model()
        
        if model is not None:
            print("✅ تم تحميل النموذج بنجاح")
            return True
        else:
            print("❌ فشل تحميل النموذج")
            return False
        
    except Exception as e:
        print(f"❌ خطأ في تحميل النموذج: {e}")
        return False

def check_requirements():
    """فحص ملف requirements.txt"""
    print("\n📋 فحص requirements.txt...")
    
    req_file = "huggingface_upload/requirements.txt"
    if os.path.exists(req_file):
        with open(req_file, 'r') as f:
            content = f.read()
        
        print("✅ محتوى requirements.txt:")
        for line in content.strip().split('\n'):
            if line.strip():
                print(f"   - {line.strip()}")
        
        # فحص إصدار Gradio
        if "gradio==4.36.1" in content:
            print("✅ إصدار Gradio محدث للتوافق")
        else:
            print("⚠️ تحقق من إصدار Gradio")
        
        return True
    else:
        print("❌ ملف requirements.txt غير موجود")
        return False

def main():
    """الدالة الرئيسية"""
    print("=" * 70)
    print("🔧 اختبار إصلاحات Hugging Face Spaces")
    print("=" * 70)
    
    tests = [
        ("فحص requirements.txt", check_requirements),
        ("استيراد Gradio", test_gradio_import),
        ("مكونات Gradio", test_gradio_components),
        ("استيراد التطبيق", test_app_import),
        ("إعدادات launch", test_launch_settings),
        ("تحميل النموذج", test_model_loading)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        try:
            if test_func():
                passed += 1
            else:
                print(f"❌ فشل اختبار: {test_name}")
        except Exception as e:
            print(f"❌ خطأ في اختبار {test_name}: {e}")
    
    print("\n" + "=" * 70)
    print(f"📊 النتائج: {passed}/{total} اختبار نجح")
    
    if passed == total:
        print("🎉 جميع الاختبارات نجحت!")
        print("✅ التطبيق جاهز للرفع على Hugging Face Spaces")
        print("🔧 تم إصلاح جميع المشاكل المعروفة")
        
        print("\n🚀 الإصلاحات المطبقة:")
        print("   ✅ تحديث Gradio إلى 4.36.1")
        print("   ✅ إضافة share=True لـ launch")
        print("   ✅ تبسيط مكونات Gradio")
        print("   ✅ إزالة enable_queue")
        print("   ✅ تحسين معالجة الأخطاء")
        
        return True
    else:
        print("❌ بعض الاختبارات فشلت")
        print("💡 راجع الأخطاء أعلاه")
        return False

if __name__ == "__main__":
    try:
        success = main()
        if not success:
            sys.exit(1)
    except KeyboardInterrupt:
        print("\n⏹️ تم إلغاء الاختبار")
    except Exception as e:
        print(f"\n❌ خطأ غير متوقع: {e}")
        sys.exit(1)
