#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
تشغيل التطبيق مع معالجة شاملة للأخطاء
Start app with comprehensive error handling
"""

import os
import sys
import time
import subprocess

def print_header():
    """طباعة العنوان"""
    print("=" * 60)
    print("🎤 محول الصوت إلى نص - تشغيل آمن")
    print("🔧 Safe Audio to Text Converter Launcher")
    print("=" * 60)

def check_python():
    """فحص إصدار Python"""
    print("🐍 فحص Python...")
    version = sys.version_info
    if version.major >= 3 and version.minor >= 8:
        print(f"✅ Python {version.major}.{version.minor}.{version.micro}")
        return True
    else:
        print(f"❌ Python {version.major}.{version.minor} - يتطلب 3.8+")
        return False

def check_modules():
    """فحص المكتبات المطلوبة"""
    print("📦 فحص المكتبات...")
    
    required = ['gradio', 'whisper', 'torch']
    missing = []
    
    for module in required:
        try:
            __import__(module)
            print(f"✅ {module}")
        except ImportError:
            print(f"❌ {module} غير مثبت")
            missing.append(module)
    
    return len(missing) == 0, missing

def install_missing(missing):
    """تثبيت المكتبات المفقودة"""
    print(f"📥 تثبيت المكتبات المفقودة: {', '.join(missing)}")
    
    try:
        subprocess.check_call([
            sys.executable, '-m', 'pip', 'install', 
            '-r', 'requirements.txt'
        ])
        print("✅ تم التثبيت بنجاح")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ فشل التثبيت: {e}")
        return False

def run_app_safe():
    """تشغيل التطبيق بأمان"""
    print("🚀 بدء تشغيل التطبيق...")
    
    # قائمة التطبيقات بترتيب الأولوية
    apps = [
        ("simple_test.py", "اختبار Gradio البسيط"),
        ("app_simple.py", "التطبيق المبسط"),
        ("app.py", "التطبيق الكامل")
    ]
    
    for app_file, description in apps:
        if not os.path.exists(app_file):
            print(f"⚠️ {app_file} غير موجود")
            continue
        
        print(f"\n🔄 محاولة تشغيل {description}...")
        print(f"📁 الملف: {app_file}")
        
        try:
            # تشغيل التطبيق
            process = subprocess.Popen([
                sys.executable, app_file
            ], stdout=subprocess.PIPE, stderr=subprocess.PIPE, text=True)
            
            # انتظار قصير للتحقق من بدء التشغيل
            time.sleep(3)
            
            if process.poll() is None:
                print(f"✅ {description} يعمل بنجاح!")
                print("🌐 افتح المتصفح على: http://127.0.0.1:7860")
                print("⏹️ للإيقاف اضغط Ctrl+C")
                
                try:
                    # انتظار انتهاء العملية
                    process.wait()
                except KeyboardInterrupt:
                    print("\n⏹️ تم إيقاف التطبيق")
                    process.terminate()
                
                return True
            else:
                # العملية انتهت بخطأ
                stdout, stderr = process.communicate()
                print(f"❌ فشل تشغيل {app_file}")
                if stderr:
                    print(f"الخطأ: {stderr[:200]}...")
                
        except Exception as e:
            print(f"❌ خطأ في تشغيل {app_file}: {e}")
    
    print("\n❌ فشل تشغيل جميع التطبيقات")
    return False

def show_manual_instructions():
    """عرض تعليمات التشغيل اليدوي"""
    print("\n" + "=" * 50)
    print("📖 تعليمات التشغيل اليدوي")
    print("=" * 50)
    
    print("\n1️⃣ تشغيل اختبار بسيط:")
    print("   python simple_test.py")
    
    print("\n2️⃣ تشغيل التطبيق المبسط:")
    print("   python app_simple.py")
    
    print("\n3️⃣ تشغيل التطبيق الكامل:")
    print("   python app.py")
    
    print("\n4️⃣ إصلاح الأخطاء:")
    print("   python fix_errors.py")
    
    print("\n5️⃣ فحص المتطلبات:")
    print("   pip list | findstr gradio")
    print("   pip list | findstr whisper")

def main():
    """الدالة الرئيسية"""
    print_header()
    
    # فحص Python
    if not check_python():
        print("❌ يرجى تحديث Python إلى 3.8 أو أحدث")
        return
    
    # فحص المكتبات
    modules_ok, missing = check_modules()
    
    if not modules_ok:
        print(f"\n⚠️ مكتبات مفقودة: {', '.join(missing)}")
        
        if os.path.exists('requirements.txt'):
            install = input("هل تريد تثبيت المكتبات المفقودة؟ (y/n): ")
            if install.lower() in ['y', 'yes', 'نعم']:
                if install_missing(missing):
                    print("✅ تم تثبيت المكتبات، إعادة فحص...")
                    modules_ok, _ = check_modules()
                else:
                    print("❌ فشل التثبيت")
                    show_manual_instructions()
                    return
        else:
            print("❌ ملف requirements.txt غير موجود")
            show_manual_instructions()
            return
    
    if modules_ok:
        print("\n✅ جميع المتطلبات متوفرة")
        
        # تشغيل التطبيق
        if not run_app_safe():
            show_manual_instructions()
    else:
        show_manual_instructions()

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n⏹️ تم إلغاء التشغيل")
    except Exception as e:
        print(f"\n❌ خطأ غير متوقع: {e}")
        show_manual_instructions()
