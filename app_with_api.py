#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
تطبيق Hugging Face Spaces مع API Key تلقائي
Hugging Face Spaces App with Auto API Key
"""

import gradio as gr
import whisper
import tempfile
import os
import secrets
import json
from pathlib import Path
import torch
from datetime import datetime
import traceback

# تحديد الجهاز المتاح
device = "cuda" if torch.cuda.is_available() else "cpu"

# إنشاء API Key تلقائي
try:
    API_KEY = f"whisper-{secrets.token_urlsafe(16)}"
except:
    API_KEY = f"whisper-{os.urandom(8).hex()}"

# تحميل النماذج المختلفة
models = {}

# إعدادات النماذج المحلية
LOCAL_MODELS = {
    "tiny": "./models/tiny.pt",
    "base": "./models/base.pt",
    "small": "./models/small.pt"
}

def load_model(model_name):
    """تحميل النموذج محلياً أو من الإنترنت"""
    if model_name not in models:
        try:
            print(f"🔄 تحميل نموذج {model_name}...")

            # البحث عن النموذج محلياً أولاً
            local_path = LOCAL_MODELS.get(model_name)
            if local_path and os.path.exists(local_path):
                print(f"✅ تم العثور على النموذج محلياً: {local_path}")
                models[model_name] = whisper.load_model(local_path, device=device)
            else:
                print(f"⬇️ تحميل النموذج من الإنترنت...")
                models[model_name] = whisper.load_model(model_name, device=device)

            print(f"✅ تم تحميل {model_name} بنجاح!")
        except Exception as e:
            print(f"❌ خطأ في تحميل النموذج {model_name}: {e}")
            # استخدام نموذج افتراضي
            if model_name != "tiny":
                print("🔄 محاولة تحميل نموذج tiny كبديل...")
                models[model_name] = whisper.load_model("tiny", device=device)
            else:
                raise e
    return models[model_name]

def verify_api_key(api_key):
    """التحقق من صحة API Key"""
    return api_key == API_KEY

def transcribe_audio(audio_file, model_name="tiny", language=None, task="transcribe", api_key=""):
    """تحويل الصوت إلى نص مع التحقق من API Key"""

    # التحقق من API Key للاستخدام البرمجي
    if api_key and not verify_api_key(api_key):
        return "❌ API Key غير صحيح", "❌ فشل التحقق من API Key"

    if audio_file is None:
        return "❌ يرجى رفع ملف صوتي أو فيديو", ""

    try:
        # تحميل النموذج
        model = load_model(model_name)

        # تحويل الملف
        if language and language != "تلقائي":
            result = model.transcribe(audio_file, language=language, task=task)
        else:
            result = model.transcribe(audio_file, task=task)

        # استخراج النتائج
        text = result["text"].strip()
        detected_language = result.get("language", "غير محدد")

        # معلومات إضافية
        info = f"""📊 معلومات التحويل:
🤖 النموذج: {model_name}
🌍 اللغة المكتشفة: {detected_language}
⚙️ المهمة: {task}
🎯 الجهاز: {device}
📝 عدد الكلمات: {len(text.split())}
🕒 الوقت: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}"""

        return text, info

    except Exception as e:
        error_msg = f"❌ خطأ في التحويل: {str(e)}"
        print(f"Error details: {traceback.format_exc()}")
        return error_msg, f"تفاصيل الخطأ: {str(e)}"

def get_api_info():
    """الحصول على معلومات API"""
    space_url = os.getenv('SPACE_ID', 'YOUR_USERNAME-YOUR_SPACE_NAME')
    
    api_info = f"""
🔑 **API Key الخاص بك:**
```
{API_KEY}
```

🌐 **رابط API:**
```
https://{space_url}.hf.space/api/predict
```

📋 **مثال على الاستخدام:**
```python
import requests
import json

API_URL = "https://{space_url}.hf.space/api/predict"
API_KEY = "{API_KEY}"

# رفع ملف وتحويله
with open("audio.mp3", "rb") as f:
    files = {{"data": f}}
    data = {{
        "data": [
            None,  # audio_file
            "small",  # model
            "Arabic",  # language
            "transcribe",  # task
            API_KEY  # api_key
        ]
    }}
    
response = requests.post(API_URL, files=files, data=json.dumps(data))
result = response.json()
print(result["data"][0])  # النص المستخرج
```
"""
    return api_info

# تحميل نموذج تلقائياً عند بدء التطبيق
print("🚀 بدء تشغيل التطبيق...")
print(f"🔑 API Key: {API_KEY}")

# محاولة تحميل النماذج بالترتيب
models_to_try = ["tiny", "base", "small"]
loaded_model = None

for model_name in models_to_try:
    try:
        print(f"⏳ محاولة تحميل نموذج {model_name}...")
        load_model(model_name)
        loaded_model = model_name
        print(f"✅ تم تحميل نموذج {model_name} بنجاح!")
        break
    except Exception as e:
        print(f"⚠️ فشل تحميل نموذج {model_name}: {e}")
        continue

if not loaded_model:
    print("⚠️ لم يتم تحميل أي نموذج محلي، سيتم التحميل عند الحاجة")

# واجهة Gradio
with gr.Blocks(
    title="محول الفيديو/الصوت إلى نص مع API",
    theme=gr.themes.Soft()
) as app:
    
    gr.HTML("""
    <div style="text-align: center;">
        <h1>🎤 محول الفيديو/الصوت إلى نص</h1>
        <h2>Video/Audio to Text Converter with API</h2>
        <p>مدعوم بـ OpenAI Whisper | Powered by OpenAI Whisper</p>
        <p>🚀 <strong>نموذج Small محمل تلقائياً | Small Model Auto-Loaded</strong></p>
    </div>
    """)
    
    with gr.Tab("🎤 تحويل | Convert"):
        with gr.Row():
            with gr.Column():
                audio_input = gr.Audio(
                    label="رفع ملف صوتي أو فيديو | Upload Audio/Video File",
                    type="filepath"
                )
                
                model_choice = gr.Dropdown(
                    choices=["tiny", "base", "small"],
                    value=loaded_model or "tiny",
                    label="اختيار النموذج | Choose Model"
                )
                
                language_choice = gr.Dropdown(
                    choices=["تلقائي", "Arabic", "English", "French", "Spanish", "German"],
                    value="تلقائي",
                    label="اللغة | Language"
                )
                
                task_choice = gr.Radio(
                    choices=["transcribe", "translate"],
                    value="transcribe",
                    label="المهمة | Task"
                )
                
                convert_btn = gr.Button("🚀 تحويل | Convert", variant="primary")
            
            with gr.Column():
                output_text = gr.Textbox(
                    label="النص المستخرج | Extracted Text",
                    lines=15,
                    max_lines=20
                )
                
                info_text = gr.Textbox(
                    label="معلومات التحويل | Conversion Info",
                    lines=8
                )
    
    with gr.Tab("🔑 API Key & Usage"):
        gr.HTML("<h3>🔐 معلومات API الخاصة بك</h3>")
        
        api_info_display = gr.Markdown(
            value=get_api_info()
        )
        
        refresh_api_btn = gr.Button("🔄 تحديث معلومات API | Refresh API Info")
        
        gr.HTML(f"""
        <div style="margin-top: 20px; padding: 15px; background: #fff3cd; border-radius: 5px;">
            <h4>⚠️ تنبيه أمني | Security Notice</h4>
            <p><strong>العربية:</strong> احتفظ بـ API Key في مكان آمن ولا تشاركه مع أحد</p>
            <p><strong>English:</strong> Keep your API Key secure and don't share it with anyone</p>
            <p><strong>Your API Key:</strong> <code>{API_KEY}</code></p>
        </div>
        """)
    
    # ربط الأحداث
    convert_btn.click(
        fn=transcribe_audio,
        inputs=[audio_input, model_choice, language_choice, task_choice, gr.State("")],
        outputs=[output_text, info_text]
    )
    
    refresh_api_btn.click(
        fn=get_api_info,
        outputs=[api_info_display]
    )

# تشغيل التطبيق
if __name__ == "__main__":
    print(f"\n🎉 التطبيق جاهز!")
    print(f"🔑 API Key: {API_KEY}")
    print(f"🌐 سيكون متاح على: https://YOUR_USERNAME-YOUR_SPACE_NAME.hf.space")
    print(f"📡 API Endpoint: https://YOUR_USERNAME-YOUR_SPACE_NAME.hf.space/api/predict")
    
    app.launch()
