# 🔧 إصلاح خطأ Internal Server Error 500

## 🚨 المشكلة
ظهور رسالة "Internal Server Error 500" عند تشغيل التطبيق على Hugging Face Spaces.

## 🔍 الأسباب المحتملة
1. **خطأ في الكود** - مشكلة في ملف التطبيق
2. **نقص في الذاكرة** - النماذج كبيرة جداً
3. **مكتبات مفقودة** - requirements.txt غير صحيح
4. **مسار ملفات خاطئ** - النماذج غير موجودة

## ✅ الحلول

### 1. استخدام التطبيق المبسط
استبدل `app_with_api.py` بـ `app_simple.py` في ملف README.md:

```yaml
---
title: محو<PERSON> الفيديو/الصوت إلى نص
emoji: 🎤
colorFrom: blue
colorTo: purple
sdk: gradio
sdk_version: 4.44.0
app_file: app_simple.py  # ← غير هذا السطر
pinned: false
license: mit
---
```

### 2. تحديث requirements.txt
تأكد من أن ملف requirements.txt يحتوي على:

```
openai-whisper>=20231117
gradio>=4.0.0
torch>=1.10.1
torchaudio>=0.10.1
numpy>=1.21.0
tiktoken>=0.5.0
```

### 3. فحص الملفات المرفوعة
تأكد من رفع هذه الملفات:

```
📁 الملفات المطلوبة:
├── README.md ✅
├── app_simple.py ✅  (بدلاً من app_with_api.py)
├── requirements.txt ✅
└── models/ (اختياري)
    ├── tiny.pt
    ├── base.pt
    └── small.pt
```

### 4. خطوات الإصلاح السريع

#### الخطوة 1: تحديث README.md
```bash
# في صفحة Space الخاص بك
# اضغط على "Files" → "README.md" → "Edit"
# غير السطر:
app_file: app_simple.py
```

#### الخطوة 2: رفع app_simple.py
```bash
# ارفع ملف app_simple.py الجديد
# احذف app_with_api.py القديم (اختياري)
```

#### الخطوة 3: إعادة تشغيل Space
```bash
# اذهب إلى Settings → Restart Space
# أو انتظر إعادة التشغيل التلقائي
```

## 🎯 مميزات التطبيق المبسط

### ✅ المميزات المحتفظ بها:
- 🎤 تحويل الصوت/الفيديو إلى نص
- 🌍 دعم اللغة العربية والإنجليزية
- 🔑 API Key تلقائي
- 📊 معلومات التحويل
- 🚀 واجهة سهلة الاستخدام

### 🔧 التحسينات:
- ⚡ أسرع في التحميل
- 🛡️ أكثر استقراراً
- 💾 استهلاك ذاكرة أقل
- 🔄 معالجة أخطاء أفضل

## 📋 مثال على الاستخدام بعد الإصلاح

```python
import requests
import json

# استبدل بالرابط الحقيقي
API_URL = "https://YOUR_USERNAME-YOUR_SPACE_NAME.hf.space/api/predict"

# تحويل ملف صوتي
with open("audio.mp3", "rb") as f:
    files = {"data": f}
    data = {"data": [None, "Arabic"]}
    
response = requests.post(API_URL, files=files, data=json.dumps(data))
result = response.json()
print("النص:", result["data"][0])
print("المعلومات:", result["data"][1])
```

## 🔍 فحص السجلات (Logs)

إذا استمر الخطأ:

1. **اذهب إلى Space الخاص بك**
2. **اضغط على "Logs"**
3. **ابحث عن رسائل الخطأ**
4. **انسخ رسالة الخطأ وأرسلها للدعم**

### رسائل خطأ شائعة:

#### خطأ الذاكرة:
```
CUDA out of memory
RuntimeError: [Errno 28] No space left on device
```
**الحل**: استخدم نموذج أصغر (tiny بدلاً من small)

#### خطأ المكتبات:
```
ModuleNotFoundError: No module named 'whisper'
ImportError: cannot import name 'xxx'
```
**الحل**: تحديث requirements.txt

#### خطأ الملفات:
```
FileNotFoundError: [Errno 2] No such file or directory
```
**الحل**: تأكد من رفع جميع الملفات المطلوبة

## 🆘 إذا لم تنجح الحلول

### خيار 1: إنشاء Space جديد
1. أنشئ Space جديد
2. ارفع الملفات المحدثة
3. استخدم `app_simple.py`

### خيار 2: استخدام نموذج أصغر فقط
```python
# في app_simple.py، غير السطر:
model = whisper.load_model("tiny")  # بدلاً من النماذج الكبيرة
```

### خيار 3: بدون نماذج محلية
احذف مجلد `models/` واتركه يحمل النماذج من الإنترنت

## 🎉 النتيجة المتوقعة

بعد تطبيق الإصلاحات:
- ✅ التطبيق يعمل بدون أخطاء
- ✅ واجهة سهلة للتحويل
- ✅ API Key ظاهر في التبويب
- ✅ تحويل سريع ودقيق

---

**💡 نصيحة**: ابدأ بالتطبيق المبسط أولاً، وبعد التأكد من عمله يمكنك إضافة المميزات المتقدمة تدريجياً.
