---
title: Audio to Text Converter - Whisper Small (Embedded)
emoji: 🎤
colorFrom: blue
colorTo: purple
sdk: gradio
sdk_version: 4.44.1
app_file: app.py
pinned: false
license: mit
---

# 🎤 محول الصوت إلى نص - Audio to Text Converter

تطبيق قوي لتحويل الملفات الصوتية والمرئية إلى نص باستخدام نموذج OpenAI Whisper Small المضمن محلياً.

A powerful application for converting audio and video files to text using embedded OpenAI Whisper Small model.

## ✨ الميزات - Features

### 🎯 الميزات الرئيسية:
- **دقة عالية**: نموذج Whisper Small كامل للحصول على أفضل دقة
- **سرعة محسنة**: النموذج مضمن محلياً - بدء فوري بدون تحميل
- **متعدد اللغات**: دعم 99+ لغة بما في ذلك العربية والإنجليزية
- **استقرار عالي**: لا مشاكل تحميل أو اتصال
- **سهل الاستخدام**: واجهة بسيطة وسريعة
- **API مدمج**: إمكانية الاستخدام البرمجي مع API Key فريد
- **مجاني**: استخدام مجاني بالكامل

### 📁 الملفات المدعومة:
- **الصوت**: MP3, WAV, M4A, FLAC, OGG
- **الفيديو**: MP4, AVI, MOV, MKV
- **الحد الأقصى**: 25 MB لكل ملف

## 🚀 كيفية الاستخدام - How to Use

### 1. رفع الملف:
- اضغط على "ارفع ملف صوتي أو فيديو"
- اختر الملف من جهازك أو سجل صوت مباشر

### 2. اختيار اللغة:
- اختر اللغة من القائمة المنسدلة
- أو اتركها على "تلقائي" للكشف التلقائي

### 3. التحويل:
- اضغط "تحويل إلى نص"
- انتظر حتى اكتمال المعالجة

### 4. النتائج:
- احصل على النص المستخرج
- راجع معلومات التحويل
- انسخ API Key للاستخدام البرمجي

## 🔑 API Usage

### الحصول على API Key:
1. افتح التطبيق
2. اذهب إلى تبويب "🔑 API Key & Usage"
3. انسخ API Key المعروض

### Python Example:
```python
import requests
import json

# استبدل بالرابط الحقيقي لـ Space الخاص بك
API_URL = "https://YOUR_USERNAME-YOUR_SPACE_NAME.hf.space/api/predict"

# رفع ملف وتحويله
with open("audio.mp3", "rb") as f:
    files = {"data": f}
    data = {"data": [None, "Arabic"]}
    
response = requests.post(API_URL, files=files, json=data)
result = response.json()

print("النص:", result["data"][0])
print("المعلومات:", result["data"][1])
```

### cURL Example:
```bash
curl -X POST \
  -F "data=@audio.mp3" \
  -F "data=Arabic" \
  https://YOUR_USERNAME-YOUR_SPACE_NAME.hf.space/api/predict
```

## 🤖 معلومات النموذج - Model Information

- **النموذج**: OpenAI Whisper Small (مضمن محلياً)
- **الحجم**: ~244 MB (محمل مسبقاً)
- **اللغات**: 99+ لغة مدعومة
- **الدقة**: عالية جداً للغة العربية والإنجليزية (95%+)
- **السرعة**: سريعة ومحسنة (بدء فوري)
- **الاستقرار**: عالي جداً (لا مشاكل تحميل)

## 🛠️ التقنيات المستخدمة - Technologies

- **Python**: لغة البرمجة الأساسية
- **Gradio**: لبناء واجهة المستخدم
- **OpenAI Whisper**: نموذج تحويل الصوت إلى نص
- **PyTorch**: مكتبة التعلم العميق
- **Hugging Face Spaces**: منصة الاستضافة

## 🔒 الأمان والخصوصية - Security & Privacy

- **API Key فريد**: يتم إنشاء مفتاح فريد لكل جلسة
- **عدم حفظ الملفات**: لا يتم حفظ الملفات المرفوعة
- **معالجة محلية**: تتم المعالجة على الخادم بدون إرسال لطرف ثالث
- **مفتوح المصدر**: الكود متاح للمراجعة

## 🎯 حالات الاستخدام - Use Cases

### للأفراد:
- تحويل المحاضرات والدروس
- تفريغ المقابلات والاجتماعات
- تحويل الملاحظات الصوتية
- ترجمة المحتوى الصوتي

### للمطورين:
- دمج في التطبيقات
- معالجة الملفات الصوتية بكميات كبيرة
- بناء حلول مخصصة
- التكامل مع أنظمة أخرى

## 📊 الأداء المتوقع - Expected Performance

### ⏱️ أوقات المعالجة:
- **ملف 1 دقيقة**: ~30-60 ثانية
- **ملف 5 دقائق**: ~2-4 دقائق
- **ملف 10 دقائق**: ~4-8 دقائق

### 🎯 دقة التحويل:
- **العربية الفصحى**: 95%+
- **العربية العامية**: 85-90%
- **الإنجليزية**: 95%+
- **لغات أخرى**: 80-95%

## 📞 الدعم والمساعدة - Support

إذا واجهت أي مشاكل:
1. تأكد من أن الملف أقل من 25 MB
2. تأكد من أن تنسيق الملف مدعوم
3. جرب لغة مختلفة إذا لم تعمل اللغة التلقائية
4. أعد تحميل الصفحة إذا توقف التطبيق

## 📄 الترخيص - License

MIT License - استخدام مجاني للجميع

## 🙏 شكر وتقدير - Acknowledgments

- **OpenAI**: لنموذج Whisper الرائع
- **Hugging Face**: لمنصة Spaces المجانية
- **Gradio**: لمكتبة واجهة المستخدم السهلة

---

**تم التطوير بـ ❤️ للمجتمع العربي والعالمي**

**Developed with ❤️ for the Arabic and global community**
